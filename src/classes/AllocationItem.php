<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents an allocation item (child of Allocation).
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class AllocationItem
{
    // --- Attributes ---
    private ?string $id            = null; // Stores the 'desordenado' ID
    private ?string $id_allocation = null; // Stores the 'desordenado' allocation ID (foreign key)
    private ?string $nombre        = null;
    private ?float  $porcentaje    = null;
    private ?float  $valor         = null;
    private ?float  $valor_bolsillo = null;
    private ?float  $valor_bolsillo_added = null;
    private ?int    $estado        = null; // 0 = Inactivo, 1 = Activo

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id            = null;
        $this->id_allocation = null;
        $this->nombre        = null;
        $this->porcentaje    = null;
        $this->valor         = null;
        $this->valor_bolsillo = null;
        $this->valor_bolsillo_added = null;
        $this->estado        = null; // Default state might be considered active (1) upon creation, adjust if needed
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of AllocationItem.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                     error_log("desordena() returned empty/null for DB ID: " . $dbId);
                     throw new Exception("Error processing AllocationItem ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }

            // Handle the foreign key id_allocation
            $dbIdAllocation = $data['id_allocation'] ?? null;
            if ($dbIdAllocation !== null) {
                $desordenadoIdAllocation = desordena((string)$dbIdAllocation);
                if ($desordenadoIdAllocation === null || trim((string)$desordenadoIdAllocation) === '') {
                     error_log("desordena() returned empty/null for DB id_allocation: " . $dbIdAllocation);
                     throw new Exception("Error processing AllocationItem id_allocation during object construction. desordena() failed.");
                }
                $objeto->id_allocation = $desordenadoIdAllocation;
            } else {
                $objeto->id_allocation = null;
            }

            // We store the 'desordenado' string IDs internally.
            $objeto->nombre        = $data['nombre'] ?? null;
            $objeto->porcentaje    = isset($data['porcentaje']) ? (float)$data['porcentaje'] : null;
            $objeto->valor         = isset($data['valor']) ? (float)$data['valor'] : null;
            $objeto->valor_bolsillo = isset($data['valor_bolsillo']) ? (float)$data['valor_bolsillo'] : null;
            $objeto->valor_bolsillo_added = isset($data['valor_bolsillo_added']) ? (float)$data['valor_bolsillo_added'] : null;
            $objeto->estado        = isset($data['estado']) ? (int)$data['estado'] : null;

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            error_log("Error constructing AllocationItem from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing AllocationItem: " . $e->getMessage());
        }
    }

    /**
     * Retrieves an AllocationItem object from the database by its 'desordenado' string ID.
     *
     * @param string $id       The 'desordenado' string ID of the AllocationItem to retrieve.
     * @param PDO    $conexion The database connection object.
     * @return self|null An AllocationItem object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM allocations_items
            WHERE id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting AllocationItem (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching AllocationItem: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting AllocationItem (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching AllocationItem: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all active AllocationItem objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of AllocationItem objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM allocations_items
            WHERE estado = :estado
            ORDER BY nombre
            SQL;

            $statement = $conexion->prepare($query);
            // Assuming 1 means active
            $statement->bindValue(":estado", 1, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing AllocationItem during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Decide whether to skip this item or re-throw
                        // For now, skip and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting AllocationItem list: " . $e->getMessage());
            throw new Exception("Database error fetching AllocationItem list: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting AllocationItem list: " . $e->getMessage());
            throw new Exception("Error fetching AllocationItem list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves all AllocationItem objects for a specific allocation.
     *
     * @param string $idAllocation The 'desordenado' string ID of the allocation.
     * @param PDO    $conexion     The database connection object.
     * @return array An array of AllocationItem objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     * @throws InvalidArgumentException If the allocation ID is invalid.
     */
    public static function getByAllocation(string $idAllocation, PDO $conexion): array
    {
        if (empty(trim($idAllocation))) {
            throw new InvalidArgumentException("Invalid allocation ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idAllocationOrdenado = ordena($idAllocation);
            if ($idAllocationOrdenado === false || $idAllocationOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided allocation ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM allocations_items
            WHERE id_allocation = :id_allocation AND estado = :estado
            ORDER BY nombre
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id_allocation", $idAllocationOrdenado, PDO::PARAM_INT);
            $statement->bindValue(":estado", 1, PDO::PARAM_INT); // Only active items
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing AllocationItem during getByAllocation for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting AllocationItem list by allocation (ID: $idAllocation / $idAllocationOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching AllocationItem list by allocation: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting AllocationItem list by allocation (ID: $idAllocation): " . $e->getMessage());
            throw new Exception("Error fetching AllocationItem list by allocation: " . $e->getMessage());
        }
    }

    /**
     * Calculates the sum of 'valor' for an array of AllocationItem objects.
     *
     * @param AllocationItem[] $allocationItems An array of AllocationItem objects.
     * @return float The total sum of 'valor'.
     */
    public static function getSumValor(array $allocationItems): float
    {
        $valorTotal = 0.0;
        foreach ($allocationItems as $allocationItem) {
            // Use getter to access private property
            if ($allocationItem instanceof self && $allocationItem->getValor() !== null) {
                $valorTotal += $allocationItem->getValor();
            }
        }
        return $valorTotal;
    }

    /**
     * Calculates the sum of 'porcentaje' for an array of AllocationItem objects.
     *
     * @param AllocationItem[] $allocationItems An array of AllocationItem objects.
     * @return float The total sum of 'porcentaje'.
     */
    public static function getSumPorcentaje(array $allocationItems): float
    {
        $porcentajeTotal = 0.0;
        foreach ($allocationItems as $allocationItem) {
            // Use getter to access private property
            if ($allocationItem instanceof self && $allocationItem->getPorcentaje() !== null) {
                $porcentajeTotal += $allocationItem->getPorcentaje();
            }
        }
        return $porcentajeTotal;
    }

    /**
     * Calculates the sum of 'valor_bolsillo' for an array of AllocationItem objects.
     *
     * @param AllocationItem[] $allocationItems An array of AllocationItem objects.
     * @return float The total sum of 'valor_bolsillo'.
     */
    public static function getSumValorBolsillo(array $allocationItems): float
    {
        $valorBolsilloTotal = 0.0;
        foreach ($allocationItems as $allocationItem) {
            // Use getter to access private property
            if ($allocationItem instanceof self && $allocationItem->getValorBolsillo() !== null) {
                $valorBolsilloTotal += $allocationItem->getValorBolsillo();
            }
        }
        return $valorBolsilloTotal;
    }

    /**
     * Retrieves AllocationItem records with their parent Allocation data for dropdown population.
     * Returns data formatted for bolsillo dropdown: {Allocation.nombre} - {AllocationItem.nombre}
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of associative arrays with keys: allocation_item_id, allocation_nombre, allocation_item_nombre, valor_bolsillo
     * @throws Exception If there is an error during the database query.
     */
    public static function getListWithAllocation(PDO $conexion): array
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $query = <<<SQL
            SELECT
                ai.id as allocation_item_id,
                a.nombre as allocation_nombre,
                ai.nombre as allocation_item_nombre,
                ai.valor_bolsillo
            FROM allocations_items ai
            INNER JOIN allocations a ON ai.id_allocation = a.id
            WHERE ai.estado = :ai_estado AND a.estado = :a_estado
            ORDER BY a.nombre ASC, ai.nombre ASC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":ai_estado", 1, PDO::PARAM_INT);
            $statement->bindValue(":a_estado", 1, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        // Convert the ordenado ID to desordenado for frontend use
                        $desordenadoId = desordena((string)$resultado['allocation_item_id']);
                        if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                            error_log("desordena() returned empty/null for allocation_item_id: " . $resultado['allocation_item_id']);
                            continue; // Skip this item
                        }

                        $listado[] = [
                            'allocation_item_id' => $desordenadoId,
                            'allocation_nombre' => $resultado['allocation_nombre'],
                            'allocation_item_nombre' => $resultado['allocation_item_nombre'],
                            'valor_bolsillo' => (float)$resultado['valor_bolsillo']
                        ];
                    } catch (Exception $e) {
                        error_log("Error processing AllocationItem during getListWithAllocation for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Skip this item and continue
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting AllocationItem list with allocation: " . $e->getMessage());
            throw new Exception("Database error fetching AllocationItem list with allocation: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting AllocationItem list with allocation: " . $e->getMessage());
            throw new Exception("Error fetching AllocationItem list with allocation: " . $e->getMessage());
        }
    }

    /**
     * Updates ONLY the valor_bolsillo field for a specific AllocationItem.
     * CRITICAL: Does NOT modify valor_bolsillo_added or any other fields.
     *
     * @param string $id The 'desordenado' string ID of the AllocationItem.
     * @param float $newValorBolsillo The new valor_bolsillo value.
     * @param PDO $conexion The database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing, ID is invalid, or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function updateValorBolsillo(string $id, float $newValorBolsillo, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for valor_bolsillo update.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID for valor_bolsillo update.");
            }

            $query = <<<SQL
            UPDATE allocations_items
            SET valor_bolsillo = :valor_bolsillo
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':valor_bolsillo', $newValorBolsillo, PDO::PARAM_STR);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            $success = $statement->execute();

            if (!$success) {
                error_log("Failed to update AllocationItem valor_bolsillo (ID: $id / $idOrdenado): " . implode(" | ", $statement->errorInfo()));
            }

            return $success;

        } catch (PDOException $e) {
            error_log("Database error updating AllocationItem valor_bolsillo (ID: $id): " . $e->getMessage());
            throw new Exception("Database error updating AllocationItem valor_bolsillo: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error updating AllocationItem valor_bolsillo (ID: $id): " . $e->getMessage());
            throw new Exception("Error updating AllocationItem valor_bolsillo: " . $e->getMessage());
        }
    }

    /**
     * Saves (inserts or updates) the current AllocationItem instance to the database.
     * Sets the 'estado' to 1 (active) implicitly during insert/update if not set.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("AllocationItem::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("AllocationItem::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            // Use the pre-determined flag for the operation
            if (!$isInsertOperation) { // It's an update
                return $this->_update($conexion);
            } else { // It's an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving AllocationItem (ID: {$idInfo}): " . $e->getMessage());
            throw new Exception("Database error saving AllocationItem: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving AllocationItem: " . $e->getMessage());
            throw new Exception("Error saving AllocationItem: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current AllocationItem instance into the database. (Private method)
     * Sets estado to 1 (active) by default on insert.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena') || !function_exists('ordena')) {
            throw new Exception("Global functions 'desordena' and 'ordena' are required but not found.");
        }

        // Assume new allocation items are active by default
        $estadoParaGuardar = $this->getEstado() ?? 1;

        // Convert id_allocation from desordenado to ordenado for database storage
        $idAllocationOrdenado = null;
        if ($this->getIdAllocation() !== null) {
            $idAllocationOrdenado = ordena($this->getIdAllocation());
            if ($idAllocationOrdenado === false || $idAllocationOrdenado <= 0) {
                throw new Exception("Failed to process the allocation ID for insert: " . $this->getIdAllocation());
            }
        }

        $query = <<<SQL
        INSERT INTO allocations_items (
            id_allocation,
            nombre,
            porcentaje,
            valor,
            estado
        ) VALUES (
            :id_allocation,
            :nombre,
            :porcentaje,
            :valor,
            :estado
        )
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':id_allocation', $idAllocationOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
        $statement->bindValue(':porcentaje', $this->getPorcentaje(), PDO::PARAM_STR); // PDO often handles float as string
        $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR); // PDO often handles float as string
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID (which is the 'ordenado' integer ID)
            $lastIdOrdenado = $conexion->lastInsertId();
            if ($lastIdOrdenado) {
                // Convert it to 'desordenado' string ID and set it on the object
                $this->setId(desordena((string)$lastIdOrdenado));
                // Also update the object properties with values actually inserted
                $this->setEstado($estadoParaGuardar);
            } else {
                 error_log("Failed to retrieve lastInsertId after AllocationItem insert.");
                 return false;
            }
        } else {
            error_log("Failed to insert AllocationItem: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current AllocationItem instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update AllocationItem without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
         if ($idOrdenado === false || $idOrdenado <= 0) {
             throw new Exception("Failed to process the AllocationItem ID for update: " . $this->getId());
         }

        // Convert id_allocation from desordenado to ordenado for database storage
        $idAllocationOrdenado = null;
        if ($this->getIdAllocation() !== null) {
            $idAllocationOrdenado = ordena($this->getIdAllocation());
            if ($idAllocationOrdenado === false || $idAllocationOrdenado <= 0) {
                throw new Exception("Failed to process the allocation ID for update: " . $this->getIdAllocation());
            }
        }

        // Use current estado if set, otherwise default to 1 (active)
        $estadoParaGuardar = $this->getEstado() ?? 1;

        $query = <<<SQL
        UPDATE allocations_items SET
             id_allocation = :id_allocation
            ,nombre = :nombre
            ,porcentaje = :porcentaje
            ,valor = :valor
            ,valor_bolsillo = :valor_bolsillo
            ,valor_bolsillo_added = :valor_bolsillo_added
            ,estado = :estado
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':id_allocation', $idAllocationOrdenado, PDO::PARAM_INT);
        $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
        $statement->bindValue(':porcentaje', $this->getPorcentaje(), PDO::PARAM_STR);
        $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
        $statement->bindValue(':valor_bolsillo', $this->getValorBolsillo(), PDO::PARAM_STR);
        $statement->bindValue(':valor_bolsillo_added', $this->getValorBolsilloAdded(), PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT); // Bind the 'ordenado' ID

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update AllocationItem (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
        } else {
             // Ensure object's estado reflects the saved state
             $this->setEstado($estadoParaGuardar);
        }

        return $success;
    }

    /**
     * Deletes (soft deletes) an AllocationItem record by setting its estado to 0.
     *
     * @param string $id       The 'desordenado' string ID of the AllocationItem to delete.
     * @param PDO    $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            UPDATE allocations_items
            SET estado = :estado
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':estado', 0, PDO::PARAM_INT); // Set estado to 0 for soft delete
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error soft-deleting AllocationItem (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error deleting AllocationItem: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena
             error_log("Error soft-deleting AllocationItem (ID: $id): " . $e->getMessage());
             throw new Exception("Error deleting AllocationItem: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the AllocationItem.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
             throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            // Validate id_allocation (required)
            if ($this->getIdAllocation() === null || empty(trim($this->getIdAllocation()))) {
                throw new Exception('Debe especificar la asignación (id_allocation)');
            }

            validar_textovacio($this->getNombre(), 'Debe especificar el nombre');

            // Validate Porcentaje
            $porcentajeOriginal = $this->getPorcentaje(); // Get the value currently set on the object
            if ($porcentajeOriginal === null) {
                 // If the value is strictly null, it's missing.
                 throw new Exception('Debe especificar el porcentaje');
            }

            // If not null, proceed to clean and validate numeric format
            $porcentajeLimpio = format_numberclean((string)$porcentajeOriginal);
            if (!is_numeric($porcentajeLimpio)) {
                 // If after cleaning it's not numeric, it's invalid.
                 throw new Exception("El porcentaje proporcionado no es numérico después de la limpieza.");
            }
            // Re-set the value on the object with the cleaned float value
            $this->setPorcentaje((float)$porcentajeLimpio);

            // Validate porcentaje range (0-100)
            if ($this->getPorcentaje() < 0 || $this->getPorcentaje() > 100) {
                throw new Exception("El porcentaje debe estar entre 0 y 100.");
            }

            // Validate Valor
            $valorOriginal = $this->getValor(); // Get the value currently set on the object
            if ($valorOriginal === null) {
                 // If the value is strictly null, it's missing.
                 throw new Exception('Debe especificar el valor');
            }

            // If not null, proceed to clean and validate numeric format
            $valorLimpio = format_numberclean((string)$valorOriginal);
            if (!is_numeric($valorLimpio)) {
                 // If after cleaning it's not numeric, it's invalid.
                 throw new Exception("El valor proporcionado no es numérico después de la limpieza.");
            }
            // Re-set the value on the object with the cleaned float value, allowing 0 and negative numbers.
            $this->setValor((float)$valorLimpio);

            // Validate estado if it's set (allow null initially, but maybe enforce 0 or 1?)
            if ($this->getEstado() !== null && !in_array($this->getEstado(), [0, 1], true)) {
                 throw new Exception("El estado del elemento de asignación no es válido (debe ser 0 o 1).");
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdAllocation(): ?string
    {
        return $this->id_allocation;
    }

    /**
     * Sets the 'desordenado' string allocation ID (foreign key).
     */
    public function setIdAllocation(?string $id_allocation): self
    {
        $this->id_allocation = $id_allocation;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getPorcentaje(): ?float
    {
        return $this->porcentaje;
    }

    public function setPorcentaje(?float $porcentaje): self
    {
        $this->porcentaje = $porcentaje;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getValorBolsillo(): ?float
    {
        return $this->valor_bolsillo;
    }

    public function setValorBolsillo(?float $valor_bolsillo): self
    {
        $this->valor_bolsillo = $valor_bolsillo;
        return $this;
    }

    public function getValorBolsilloAdded(): ?float
    {
        return $this->valor_bolsillo_added;
    }

    public function setValorBolsilloAdded(?float $valor_bolsillo_added): self
    {
        $this->valor_bolsillo_added = $valor_bolsillo_added;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        // Optional: Add validation here too, e.g., ensure $estado is 0 or 1
        // if ($estado !== null && !in_array($estado, [0, 1])) {
        //     throw new InvalidArgumentException("Estado must be 0, 1, or null.");
        // }
        $this->estado = $estado;
        return $this;
    }
}
