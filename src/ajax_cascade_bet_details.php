<?php
/**
 * AJAX endpoint to cascade win/loss status from a PartidoBet to all its associated PartidoBetDetalle records.
 * This is used when closing an entire bet to automatically update all bet detail records with the same result.
 */

// Set content type to JSON
header('Content-Type: application/json');

global $conexion;

use App\classes\PartidoBet;
use App\classes\PartidoBetDetalle;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // File-based logging for debugging
    $logFile = __ROOT__ . '/cascade_debug.log';
    file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] CASCADE ENDPOINT CALLED\n", FILE_APPEND);
    file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] POST data: " . print_r($_POST, true) . "\n", FILE_APPEND);

    // Validate required parameters
    if (!isset($_POST['bet_id']) || empty(trim($_POST['bet_id']))) {
        throw new Exception('ID de apuesta requerido');
    }

    if (!isset($_POST['result']) || !in_array($_POST['result'], ['won', 'lost'])) {
        throw new Exception('Resultado de apuesta inválido');
    }

    $betId = trim($_POST['bet_id']);
    $isWon = ($_POST['result'] === 'won');

    // Verify that the bet exists and is closed
    $bet = PartidoBet::get($betId, $conexion);
    if (!$bet) {
        throw new Exception('Apuesta no encontrada');
    }

    if ($bet->getEsCerrado() !== 1) {
        throw new Exception('La apuesta debe estar cerrada antes de cascadear el resultado');
    }

    // Get all bet details for this bet
    $betDetails = PartidoBetDetalle::getByPartidoBet($betId, $conexion);

    // Debug logging
    file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Bet ID: $betId, Found " . count($betDetails) . " bet details\n", FILE_APPEND);

    // Additional debugging - check if the bet ID conversion is working
    if (!function_exists('ordena')) {
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] ERROR: ordena function not found\n", FILE_APPEND);
    } else {
        $betIdOrdenado = ordena($betId);
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Bet ID conversion: $betId -> $betIdOrdenado\n", FILE_APPEND);

        // Direct database query to verify relationship
        $directQuery = "SELECT COUNT(*) as count FROM partidos_bets_detalles WHERE id_partido_bet = :id_partido_bet";
        $directStatement = $conexion->prepare($directQuery);
        $directStatement->bindValue(':id_partido_bet', $betIdOrdenado, PDO::PARAM_INT);
        $directStatement->execute();
        $directResult = $directStatement->fetch(PDO::FETCH_ASSOC);
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Direct DB query found " . $directResult['count'] . " records\n", FILE_APPEND);
    }

    if (empty($betDetails)) {
        // No details to update, but this is not an error
        echo json_encode([
            'success' => true,
            'message' => 'No hay detalles de apuesta para actualizar',
            'updated_count' => 0,
            'bet_details' => [],
            'debug_info' => [
                'bet_id' => $betId,
                'bet_found' => $bet ? 'yes' : 'no',
                'bet_closed' => $bet ? $bet->getEsCerrado() : 'N/A'
            ]
        ]);
        exit;
    }

    // Update all bet details with the cascaded result
    $updatedCount = 0;
    $updatedDetails = [];

    foreach ($betDetails as $detail) {
        try {
            $detailId = $detail->getId();
            $isCerrado = $detail->getIsCerrado();

            // Debug logging for each detail
            error_log("CASCADE DEBUG: Processing detail ID: $detailId, is_cerrado: $isCerrado");

            // Only update if the detail is not already closed
            if ($detail->getIsCerrado() !== 1) {
                error_log("CASCADE DEBUG: Calling closeBetDetail for detail ID: $detailId");
                $success = PartidoBetDetalle::closeBetDetail($detail->getId(), $isWon, $conexion);
                error_log("CASCADE DEBUG: closeBetDetail result for $detailId: " . ($success ? 'SUCCESS' : 'FAILED'));

                if ($success) {
                    $updatedCount++;
                    $updatedDetails[] = [
                        'id' => $detail->getId(),
                        'result' => $isWon ? 'won' : 'lost'
                    ];
                }
            } else {
                // Detail is already closed, but we still need to update its result to match the parent
                // We'll do a direct database update for already closed details
                error_log("CASCADE DEBUG: Detail $detailId already closed, updating is_ganado directly");

                if (!function_exists('ordena')) {
                    throw new Exception("Global function 'ordena' is required but not found.");
                }

                $idOrdenado = ordena($detail->getId());
                if ($idOrdenado !== false && $idOrdenado > 0) {
                    $query = "UPDATE partidos_bets_detalles SET is_ganado = :is_ganado WHERE id = :id";
                    $statement = $conexion->prepare($query);
                    $statement->bindValue(':is_ganado', $isWon ? 1 : 0, PDO::PARAM_INT);
                    $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

                    $executeResult = $statement->execute();
                    error_log("CASCADE DEBUG: Direct update for $detailId (ordenado: $idOrdenado): " . ($executeResult ? 'SUCCESS' : 'FAILED'));

                    if ($executeResult) {
                        $updatedCount++;
                        $updatedDetails[] = [
                            'id' => $detail->getId(),
                            'result' => $isWon ? 'won' : 'lost'
                        ];
                    }
                }
            }
        } catch (Exception $e) {
            // Log the error but continue with other details
            error_log("CASCADE ERROR: Error updating bet detail {$detail->getId()}: " . $e->getMessage());
        }
    }

    // Final debug logging
    error_log("CASCADE DEBUG: Final result - Updated $updatedCount out of " . count($betDetails) . " details");

    echo json_encode([
        'success' => true,
        'message' => "Resultado cascadeado exitosamente a {$updatedCount} detalles de apuesta",
        'updated_count' => $updatedCount,
        'bet_details' => $updatedDetails,
        'result' => $_POST['result'],
        'debug_info' => [
            'total_details_found' => count($betDetails),
            'details_updated' => $updatedCount,
            'bet_id' => $betId,
            'is_won' => $isWon
        ]
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
